9999999999O:26:"App\Models\SettingsGeneral":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"settings_general";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:5:"title";s:7:"Moelive";s:8:"subtitle";s:12:"积分市场";s:9:"separator";s:1:"|";s:7:"logo_id";i:14;s:12:"logo_dark_id";i:15;s:10:"favicon_id";i:16;s:20:"header_announce_text";N;s:20:"header_announce_link";N;s:20:"is_language_switcher";i:0;s:16:"default_language";s:2:"cn";}s:11:" * original";a:11:{s:2:"id";i:1;s:5:"title";s:7:"Moelive";s:8:"subtitle";s:12:"积分市场";s:9:"separator";s:1:"|";s:7:"logo_id";i:14;s:12:"logo_dark_id";i:15;s:10:"favicon_id";i:16;s:20:"header_announce_text";N;s:20:"header_announce_link";N;s:20:"is_language_switcher";i:0;s:16:"default_language";s:2:"cn";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:5:"title";i:1;s:8:"subtitle";i:2;s:9:"separator";i:3;s:7:"logo_id";i:4;s:12:"logo_dark_id";i:5;s:10:"favicon_id";i:6;s:20:"header_announce_text";i:7;s:20:"header_announce_link";i:8;s:20:"is_language_switcher";i:9;s:16:"default_language";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}